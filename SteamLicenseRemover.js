    // ==UserScript==
    // @name         Steam License Remover
    // @namespace
    // @version      2.0
    // @description  从您的Steam库中移除任何"免费"游戏，通过从您的账户中移除游戏许可证。
    // <AUTHOR>
    // @fork_comission  Beardox
    // @match        https://store.steampowered.com/account/licenses/


let removedCount = 0;

async function removeGame(id) {
    console.log(`正在移除游戏ID ${id}...`);
    try {
        const response = await fetch('https://store.steampowered.com/account/removelicense', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded' // 修改了Content-Type
            },
            body: `sessionid=${encodeURIComponent(g_sessionID)}&packageid=${encodeURIComponent(id)}` // 修正了body对象
        });

        if (response.ok) {
            const data = await response.json();
            if (data.success === 84) {
                console.log(`检测到频率限制 (success: 84)。等待10分钟后继续...`);
                await new Promise(resolve => setTimeout(resolve, 10 * 60 * 1000)); // 等待10分钟
                console.log(`等待完成。重新尝试移除游戏ID ${id}...`);
                // 递归重试当前游戏
                return await removeGame(id);
            } else if (data.success) {
                removedCount++;
                console.log(`游戏ID ${id} 移除成功。总共移除游戏数: ${removedCount}`);
            } else {
                console.log(`移除游戏ID ${id} 失败。`);
            }
        } else {
            console.log(`移除游戏ID ${id} 失败。状态: ${response.status} - ${response.statusText}`);
        }
    } catch (error) {
        console.error(`移除游戏ID ${id} 时发生错误:`, error);
    }
}

function extractIdFromLink(link) {
    const match = link.match(/RemoveFreeLicense\(\s*(\d+)\s*,/);
    return match ? match[1] : null;
}

function countRemovableGames() {
    const removeLinks = document.querySelectorAll('a[href^="javascript:RemoveFreeLicense"]');
    const totalGames = removeLinks.length;
    console.log(`Total removable games: ${totalGames}`);
    return totalGames;
}

async function removeGames() {
    const totalGames = countRemovableGames();
    const intervalID = setInterval(() => {
        console.log(`Games removed: ${removedCount} of ${totalGames}`);
        if (removedCount >= totalGames) {
            clearInterval(intervalID);
        }
    }, 1000);

    const removeLinks = document.querySelectorAll('a[href^="javascript:RemoveFreeLicense"]');
    for (const link of removeLinks) {
        const id = extractIdFromLink(link.href);
        if (id) {
            await removeGame(id);
            await new Promise(resolve => setTimeout(resolve, 2000)); // Aguarda 2 segundos antes de processar o próximo link
        } else {
            console.log(`Failed to extract ID from link: ${link.href}`);
        }
    }

    console.log(`All games removed. Total games removed: ${removedCount}`);
}

removeGames();

